"""
Voice session handling with queue integration.

This module provides the main entry point for voice calls, integrating
the Redis-based queue system for load-shedding and FIFO call management.
"""
import asyncio
import logging
import os
import time
from typing import Dict, Any, Optional
from uuid import uuid4

import httpx
from fastapi import HTTPException

from services.queue_service import get_queue_service
from workers.queue_worker import get_queue_worker

logger = logging.getLogger(__name__)


async def start_voice_session(
    call_control_id: str,
    telnyx_rtc_session_id: str,
    tenant_id: str,
    lang: str = "en",
    call_id: Optional[str] = None,
    phone_number: Optional[str] = None,
    caller_name: Optional[str] = None,
    from_queue: bool = False,
    **kwargs
) -> Dict[str, Any]:
    """
    Start a voice session with queue integration.
    
    This is the main entry point for all voice calls, whether they come
    directly from webhooks or are dequeued from the Redis queue.
    
    Args:
        call_control_id: Telnyx call control ID
        telnyx_rtc_session_id: Telnyx RTC session ID
        tenant_id: Tenant identifier
        lang: Language code (default: "en")
        call_id: Unique call identifier (generated if not provided)
        phone_number: Caller's phone number
        caller_name: Caller's name
        from_queue: Whether this call came from the queue
        **kwargs: Additional arguments
        
    Returns:
        Dictionary with session start result
    """
    if not call_id:
        call_id = f"call_{int(time.time() * 1000)}_{str(uuid4())[:8]}"
    
    logger.info(
        f"Starting voice session: call_id={call_id}, tenant_id={tenant_id}, "
        f"from_queue={from_queue}, lang={lang}"
    )
    
    try:
        # If this call is not from the queue, check capacity and potentially queue it
        if not from_queue:
            queue_service = get_queue_service()
            
            # Check if tenant is at capacity
            active_count = await queue_service.increment_active(tenant_id)
            
            if active_count > queue_service.max_active_calls_per_tenant:
                # Over capacity, decrement and try to queue
                await queue_service.decrement_active(tenant_id)
                
                # Prepare metadata for queueing
                ws_meta = {
                    "call_control_id": call_control_id,
                    "telnyx_rtc_session_id": telnyx_rtc_session_id,
                    "lang": lang,
                    "phone_number": phone_number,
                    "caller_name": caller_name
                }
                
                # Try to enqueue the call
                enqueued = await queue_service.enqueue_call(tenant_id, call_id, ws_meta)
                
                if not enqueued:
                    # Queue is full, play busy tone and hang up
                    logger.warning(
                        f"Call {call_id} rejected: tenant {tenant_id} at capacity and queue full"
                    )
                    await play_busy_and_hangup(call_control_id)
                    return {
                        "status": "rejected",
                        "reason": "capacity_exceeded",
                        "call_id": call_id,
                        "tenant_id": tenant_id
                    }
                else:
                    # Successfully queued, play hold message
                    logger.info(f"Call {call_id} queued for tenant {tenant_id}")
                    await play_hold_message(call_control_id)
                    return {
                        "status": "queued",
                        "call_id": call_id,
                        "tenant_id": tenant_id,
                        "queue_position": await queue_service.get_queue_length(tenant_id)
                    }
        
        # Start the actual voice pipeline
        result = await _start_pipecat_pipeline(
            call_control_id=call_control_id,
            telnyx_rtc_session_id=telnyx_rtc_session_id,
            tenant_id=tenant_id,
            call_id=call_id,
            lang=lang,
            phone_number=phone_number,
            caller_name=caller_name
        )
        
        logger.info(f"Voice session started successfully for call {call_id}")
        
        return {
            "status": "started",
            "call_id": call_id,
            "tenant_id": tenant_id,
            "pipeline_result": result
        }
        
    except Exception as e:
        logger.error(f"Failed to start voice session for call {call_id}: {e}", exc_info=True)
        
        # If we incremented active count but failed to start, decrement it
        if not from_queue:
            try:
                queue_service = get_queue_service()
                await queue_service.decrement_active(tenant_id)
            except Exception as cleanup_error:
                logger.error(f"Failed to cleanup active count for {tenant_id}: {cleanup_error}")
        
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start voice session: {str(e)}"
        )


async def end_voice_session(tenant_id: str, call_id: str) -> None:
    """
    End a voice session and potentially spawn the next queued call.
    
    This should be called when a call ends to:
    1. Decrement the active call count
    2. Trigger processing of the next queued call if any
    
    Args:
        tenant_id: Tenant identifier
        call_id: Call identifier that ended
    """
    logger.info(f"Ending voice session: call_id={call_id}, tenant_id={tenant_id}")
    
    try:
        queue_service = get_queue_service()
        queue_worker = get_queue_worker()
        
        # Decrement active count
        active_count = await queue_service.decrement_active(tenant_id)
        
        logger.info(f"Active calls for tenant {tenant_id} after call end: {active_count}")
        
        # Spawn next call if there are queued calls and capacity
        await queue_worker.spawn_next_call(tenant_id)
        
    except Exception as e:
        logger.error(f"Error ending voice session for call {call_id}: {e}", exc_info=True)


async def play_hold_message(call_control_id: str) -> None:
    """
    Play hold message to caller while they wait in queue.
    
    Args:
        call_control_id: Telnyx call control ID
    """
    try:
        hold_message_url = os.getenv(
            "HOLD_MESSAGE_URL",
            "https://cdn.example.com/hold-music.mp3"  # Default placeholder
        )
        
        # Use Telnyx API to play hold message
        telnyx_api_key = os.getenv("TELNYX_API_KEY")
        if not telnyx_api_key:
            logger.warning("TELNYX_API_KEY not set, cannot play hold message")
            return
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"https://api.telnyx.com/v2/calls/{call_control_id}/actions/playback_start",
                headers={
                    "Authorization": f"Bearer {telnyx_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "audio_url": hold_message_url,
                    "loop": True,
                    "overlay": False
                },
                timeout=10.0
            )
            
            if response.status_code == 200:
                logger.info(f"Hold message started for call {call_control_id}")
            else:
                logger.error(
                    f"Failed to start hold message for call {call_control_id}: "
                    f"{response.status_code} {response.text}"
                )
                
    except Exception as e:
        logger.error(f"Error playing hold message for call {call_control_id}: {e}")


async def play_busy_and_hangup(call_control_id: str) -> None:
    """
    Play busy tone and hang up the call.
    
    Args:
        call_control_id: Telnyx call control ID
    """
    try:
        telnyx_api_key = os.getenv("TELNYX_API_KEY")
        if not telnyx_api_key:
            logger.warning("TELNYX_API_KEY not set, cannot play busy tone")
            return
        
        async with httpx.AsyncClient() as client:
            # Play busy tone
            await client.post(
                f"https://api.telnyx.com/v2/calls/{call_control_id}/actions/playback_start",
                headers={
                    "Authorization": f"Bearer {telnyx_api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "audio_url": "https://www.soundjay.com/misc/sounds/bell-ringing-05.wav",  # Placeholder
                    "loop": False,
                    "overlay": False
                },
                timeout=10.0
            )
            
            # Wait a moment then hang up
            await asyncio.sleep(2.0)
            
            # Hang up the call
            await client.post(
                f"https://api.telnyx.com/v2/calls/{call_control_id}/actions/hangup",
                headers={
                    "Authorization": f"Bearer {telnyx_api_key}",
                    "Content-Type": "application/json"
                },
                timeout=10.0
            )
            
            logger.info(f"Busy tone played and call hung up: {call_control_id}")
            
    except Exception as e:
        logger.error(f"Error playing busy tone for call {call_control_id}: {e}")


async def _start_pipecat_pipeline(
    call_control_id: str,
    telnyx_rtc_session_id: str,
    tenant_id: str,
    call_id: str,
    lang: str = "en",
    phone_number: Optional[str] = None,
    caller_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    Start the actual Pipecat voice pipeline.
    
    This is a placeholder for the actual pipeline implementation.
    In a real implementation, this would start the voice agent pipeline.
    
    Args:
        call_control_id: Telnyx call control ID
        telnyx_rtc_session_id: Telnyx RTC session ID
        tenant_id: Tenant identifier
        call_id: Call identifier
        lang: Language code
        phone_number: Caller's phone number
        caller_name: Caller's name
        
    Returns:
        Dictionary with pipeline start result
    """
    # TODO: Implement actual Pipecat pipeline integration
    # This would typically:
    # 1. Create a Telnyx transport
    # 2. Set up the voice agent pipeline
    # 3. Start the conversation
    
    logger.info(
        f"Starting Pipecat pipeline for call {call_id} "
        f"(control_id={call_control_id}, session_id={telnyx_rtc_session_id})"
    )
    
    # Simulate pipeline startup
    await asyncio.sleep(0.1)
    
    # Register cleanup callback for when the call ends
    asyncio.create_task(_monitor_call_end(tenant_id, call_id, call_control_id))
    
    return {
        "pipeline_started": True,
        "call_control_id": call_control_id,
        "telnyx_rtc_session_id": telnyx_rtc_session_id,
        "language": lang
    }


async def _monitor_call_end(tenant_id: str, call_id: str, call_control_id: str) -> None:
    """
    Monitor for call end and trigger cleanup.
    
    This is a placeholder implementation. In a real system, this would
    listen for call end events from Telnyx or the voice pipeline.
    
    Args:
        tenant_id: Tenant identifier
        call_id: Call identifier
        call_control_id: Telnyx call control ID
    """
    try:
        # TODO: Implement actual call monitoring
        # This could listen for:
        # - Telnyx webhook events
        # - Pipeline completion events
        # - WebSocket disconnections
        
        # For now, simulate a call duration
        call_duration = 30.0  # Simulate 30 second call
        await asyncio.sleep(call_duration)
        
        logger.info(f"Call {call_id} ended after {call_duration}s")
        
        # Trigger cleanup
        await end_voice_session(tenant_id, call_id)
        
    except Exception as e:
        logger.error(f"Error monitoring call end for {call_id}: {e}")
        # Still try to cleanup
        try:
            await end_voice_session(tenant_id, call_id)
        except Exception as cleanup_error:
            logger.error(f"Failed to cleanup after monitoring error: {cleanup_error}")
